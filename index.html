<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lesson Creator - YouTube Video Transcription System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        background: 'oklch(1 0 0)',
                        foreground: 'oklch(0.145 0 0)',
                        card: 'oklch(1 0 0)',
                        'card-foreground': 'oklch(0.145 0 0)',
                        primary: 'oklch(0.205 0 0)',
                        'primary-foreground': 'oklch(0.985 0 0)',
                        secondary: 'oklch(0.97 0 0)',
                        'secondary-foreground': 'oklch(0.205 0 0)',
                        muted: 'oklch(0.97 0 0)',
                        'muted-foreground': 'oklch(0.556 0 0)',
                        accent: 'oklch(0.97 0 0)',
                        'accent-foreground': 'oklch(0.205 0 0)',
                        border: 'oklch(0.922 0 0)',
                        input: 'oklch(0.922 0 0)',
                        ring: 'oklch(0.708 0 0)'
                    },
                    borderRadius: {
                        'lg': '0.625rem',
                        'md': 'calc(0.625rem - 2px)',
                        'sm': 'calc(0.625rem - 4px)'
                    }
                }
            },
            darkMode: 'class'
        }
    </script>
    <style>
        .dark {
            --background: oklch(0.145 0 0);
            --foreground: oklch(0.985 0 0);
            --card: oklch(0.205 0 0);
            --card-foreground: oklch(0.985 0 0);
            --primary: oklch(0.922 0 0);
            --primary-foreground: oklch(0.205 0 0);
            --secondary: oklch(0.269 0 0);
            --secondary-foreground: oklch(0.985 0 0);
            --muted: oklch(0.269 0 0);
            --muted-foreground: oklch(0.708 0 0);
            --accent: oklch(0.269 0 0);
            --accent-foreground: oklch(0.985 0 0);
            --border: oklch(1 0 0 / 10%);
            --input: oklch(1 0 0 / 15%);
            --ring: oklch(0.556 0 0);
        }

        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
            border-color: #fde68a;
        }
        .status-downloading {
            background-color: #dbeafe;
            color: #1e40af;
            border-color: #93c5fd;
        }
        .status-processing {
            background-color: #e9d5ff;
            color: #7c3aed;
            border-color: #c4b5fd;
        }
        .status-transcribing {
            background-color: #e0e7ff;
            color: #3730a3;
            border-color: #a5b4fc;
        }
        .status-completed {
            background-color: #dcfce7;
            color: #166534;
            border-color: #86efac;
        }
        .status-failed {
            background-color: #fee2e2;
            color: #991b1b;
            border-color: #fca5a5;
        }

        /* Tab Navigation Styles */
        .tab-button {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
            color: #6b7280;
            background-color: transparent;
            border: none;
            cursor: pointer;
            white-space: nowrap;
        }

        .tab-button:hover {
            color: #374151;
            background-color: rgba(0, 0, 0, 0.05);
        }

        .tab-button.active {
            background-color: white;
            color: #111827;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .dark .tab-button {
            color: #9ca3af;
        }

        .dark .tab-button:hover {
            color: #d1d5db;
            background-color: rgba(255, 255, 255, 0.05);
        }

        .dark .tab-button.active {
            background-color: #374151;
            color: #f9fafb;
        }

        /* Responsive tab navigation */
        @media (max-width: 768px) {
            .tab-navigation {
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .tab-navigation::-webkit-scrollbar {
                display: none;
            }

            .tab-button {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
                min-width: max-content;
            }
        }

        /* Improved form styling */
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            background-color: white;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .dark .form-input {
            background-color: #374151;
            border-color: #4b5563;
            color: #f9fafb;
        }

        .dark .form-input:focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }

        /* Button improvements */
        .btn-primary {
            background-color: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary:hover:not(:disabled) {
            background-color: #2563eb;
        }

        .btn-primary:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            font-size: 0.875rem;
        }

        .btn-secondary:hover {
            background-color: #e5e7eb;
        }

        .dark .btn-secondary {
            background-color: #4b5563;
            color: #f9fafb;
        }

        .dark .btn-secondary:hover {
            background-color: #6b7280;
        }

        /* Card improvements */
        .card {
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .dark .card {
            background-color: #374151;
            border-color: #4b5563;
        }

        /* Status badge improvements */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid;
        }

        /* Loading state */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Improved scrollbar for webkit browsers */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .dark .custom-scrollbar::-webkit-scrollbar-track {
            background: #1e293b;
        }

        .dark .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #475569;
        }

        .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #64748b;
        }

        /* Improved focus states */
        .focus-ring:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Better text truncation */
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-background text-foreground min-h-screen">
    <!-- Header -->
    <header class="border-b border-border bg-card">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-primary">Lesson Creator</h1>
                <div class="flex items-center gap-4">
                    <button id="darkModeToggle" class="p-2 rounded-lg border border-border hover:bg-accent transition-colors">
                        <span class="dark:hidden">🌙</span>
                        <span class="hidden dark:inline">☀️</span>
                    </button>
                    <div class="text-sm text-muted-foreground">
                        YouTube Video Transcription System
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Navigation Tabs -->
        <div class="mb-8">
            <nav class="tab-navigation flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg overflow-x-auto">
                <button class="tab-button active" data-tab="process">
                    <span class="hidden sm:inline">Process </span>Videos
                </button>
                <button class="tab-button" data-tab="chapters">
                    <span class="hidden sm:inline">Manage </span>Chapters
                </button>
                <button class="tab-button" data-tab="transcriptions">
                    <span class="hidden sm:inline">View </span>Transcriptions
                </button>
                <button class="tab-button" data-tab="status">
                    <span class="hidden sm:inline">Processing </span>Status
                </button>
            </nav>
        </div>

        <!-- Process Videos Tab -->
        <div id="process-tab" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Video Processing Form -->
                <div class="card">
                    <h2 class="text-xl font-semibold mb-4">Process YouTube Video</h2>
                    <form id="videoForm" class="space-y-4">
                        <div>
                            <label for="videoUrl" class="block text-sm font-medium mb-2">YouTube URL</label>
                            <input
                                type="url"
                                id="videoUrl"
                                name="url"
                                placeholder="https://www.youtube.com/watch?v=... or https://www.youtube.com/playlist?list=..."
                                class="form-input"
                                required
                            >
                            <p class="text-xs text-muted-foreground mt-1">
                                Supports individual video URLs and playlists. Playlists will process all videos automatically.
                            </p>
                        </div>

                        <div>
                            <label for="chapterSelect" class="block text-sm font-medium mb-2">Chapter (Optional)</label>
                            <select
                                id="chapterSelect"
                                name="chapter_id"
                                class="form-input"
                            >
                                <option value="">Select a chapter...</option>
                            </select>
                        </div>

                        <button
                            type="submit"
                            class="btn-primary w-full"
                            id="processButton"
                        >
                            <span id="processButtonText">Process Video</span>
                            <div id="processSpinner" class="spinner hidden"></div>
                        </button>
                    </form>
                </div>

                <!-- Recent Videos -->
                <div class="card">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold">Recent Videos</h2>
                        <div id="recentVideosBulkActions" class="hidden flex items-center gap-2">
                            <button
                                id="selectAllRecentVideos"
                                class="text-xs bg-secondary text-secondary-foreground px-3 py-1 rounded hover:bg-secondary/90 transition-colors"
                            >
                                Select All
                            </button>
                            <button
                                id="bulkDeleteRecentVideos"
                                class="text-xs bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 transition-colors"
                                disabled
                            >
                                Delete Selected
                            </button>
                        </div>
                    </div>
                    <div id="recentVideos" class="space-y-3">
                        <div class="text-center text-muted-foreground py-8">
                            No videos processed yet
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chapters Tab -->
        <div id="chapters-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Add Chapter Form -->
                <div class="card">
                    <h2 class="text-xl font-semibold mb-4">Add New Chapter</h2>
                    <form id="chapterForm" class="space-y-4">
                        <div>
                            <label for="chapterName" class="block text-sm font-medium mb-2">Chapter Name</label>
                            <input
                                type="text"
                                id="chapterName"
                                name="name"
                                placeholder="Enter chapter name..."
                                class="form-input"
                                required
                            >
                        </div>

                        <div>
                            <label for="chapterDescription" class="block text-sm font-medium mb-2">Description (Optional)</label>
                            <textarea
                                id="chapterDescription"
                                name="description"
                                placeholder="Enter chapter description..."
                                rows="3"
                                class="form-input"
                            ></textarea>
                        </div>

                        <button
                            type="submit"
                            class="btn-primary w-full"
                        >
                            Add Chapter
                        </button>
                    </form>
                </div>

                <!-- Chapters List -->
                <div class="card">
                    <h2 class="text-xl font-semibold mb-4">Existing Chapters</h2>
                    <div id="chaptersList" class="space-y-3">
                        <div class="text-center text-muted-foreground py-8">
                            No chapters created yet
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transcriptions Tab -->
        <div id="transcriptions-tab" class="tab-content hidden">
            <div class="space-y-6">
                <!-- Filters -->
                <div class="card">
                    <h2 class="text-xl font-semibold mb-4">Filter Transcriptions</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="filterChapter" class="block text-sm font-medium mb-2">Filter by Chapter</label>
                            <select
                                id="filterChapter"
                                class="form-input"
                            >
                                <option value="">All Chapters</option>
                            </select>
                        </div>

                        <div>
                            <label for="searchTranscriptions" class="block text-sm font-medium mb-2">Search</label>
                            <input
                                type="text"
                                id="searchTranscriptions"
                                placeholder="Search transcriptions..."
                                class="form-input"
                            >
                        </div>

                        <div class="flex items-end">
                            <button
                                id="exportTranscriptions"
                                class="btn-secondary w-full"
                            >
                                Export All
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Transcriptions List -->
                <div class="card">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold">Transcriptions</h2>
                        <div id="transcriptionsBulkActions" class="hidden flex items-center gap-2">
                            <button
                                id="selectAllTranscriptions"
                                class="text-xs bg-secondary text-secondary-foreground px-3 py-1 rounded hover:bg-secondary/90 transition-colors"
                            >
                                Select All
                            </button>
                            <button
                                id="bulkDeleteTranscriptions"
                                class="text-xs bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 transition-colors"
                                disabled
                            >
                                Delete Selected
                            </button>
                        </div>
                    </div>
                    <div id="transcriptionsList" class="space-y-4">
                        <div class="text-center text-muted-foreground py-8">
                            No transcriptions available
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Tab -->
        <div id="status-tab" class="tab-content hidden">
            <div class="space-y-6">
                <!-- Processing Queue -->
                <div class="card">
                    <h2 class="text-xl font-semibold mb-4">Processing Status</h2>
                    <div id="processingStatus" class="space-y-3">
                        <div class="text-center text-muted-foreground py-8">
                            No videos in processing queue
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="card text-center">
                        <div class="text-2xl font-bold text-blue-600" id="totalVideos">0</div>
                        <div class="text-sm text-gray-600">Total Videos</div>
                    </div>
                    <div class="card text-center">
                        <div class="text-2xl font-bold text-blue-600" id="totalTranscriptions">0</div>
                        <div class="text-sm text-gray-600">Total Transcriptions</div>
                    </div>
                    <div class="card text-center">
                        <div class="text-2xl font-bold text-blue-600" id="totalChapters">0</div>
                        <div class="text-sm text-gray-600">Total Chapters</div>
                    </div>
                </div>

                <!-- File Cleanup -->
                <div class="card">
                    <h2 class="text-xl font-semibold mb-4">File Management</h2>
                    <div class="space-y-4">
                        <p class="text-sm text-muted-foreground">
                            The system automatically cleans up video and audio files after processing to keep storage clean.
                            You can also manually clean up files if needed.
                        </p>

                        <div class="flex flex-col sm:flex-row gap-3">
                            <button
                                id="cleanupAllBtn"
                                class="btn-secondary flex-1"
                            >
                                <span id="cleanupAllText">Clean Up All Completed Videos</span>
                                <div id="cleanupAllSpinner" class="spinner hidden ml-2"></div>
                            </button>

                            <div class="text-xs text-muted-foreground self-center">
                                This will remove video and audio files for all completed transcriptions
                            </div>
                        </div>

                        <div id="cleanupResult" class="hidden p-3 rounded-lg"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Modal for Transcription Details -->
    <div id="transcriptionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="card max-w-4xl max-h-[90vh] w-full mx-4 overflow-hidden shadow-2xl">
            <div class="p-6 border-b border-border flex items-center justify-between">
                <h3 id="modalTitle" class="text-lg font-semibold">Transcription Details</h3>
                <button id="closeModal" class="text-muted-foreground hover:text-foreground">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6 overflow-y-auto max-h-[70vh]">
                <div id="modalContent" class="space-y-4">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
            <div class="p-6 border-t border-border flex justify-end gap-3">
                <button id="copyTranscription" class="btn-primary">
                    Copy to Clipboard
                </button>
                <button id="closeModalBtn" class="btn-secondary">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/app.js"></script>
</body>
</html>