const express = require('express');
const router = express.Router();
const Transcription = require('../models/Transcription');
const TranscriptionService = require('../services/transcriptionService');

// Initialize transcription service
const transcriptionService = new TranscriptionService();

// Get all transcriptions
router.get('/', async (req, res) => {
  try {
    const { chapter_id, with_details } = req.query;

    let transcriptions;
    
    if (chapter_id) {
      transcriptions = await Transcription.findByChapterId(chapter_id);
    } else if (with_details === 'true') {
      transcriptions = await Transcription.findAllWithDetails();
    } else {
      transcriptions = await Transcription.findAll();
      transcriptions = transcriptions.map(t => t.toJSON());
    }

    res.json({
      success: true,
      data: transcriptions
    });
  } catch (error) {
    console.error('Error fetching transcriptions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch transcriptions',
      message: error.message
    });
  }
});

// Get transcription by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const transcription = await Transcription.findById(id);
    
    if (!transcription) {
      return res.status(404).json({
        success: false,
        error: 'Transcription not found'
      });
    }

    res.json({
      success: true,
      data: transcription.toJSON()
    });
  } catch (error) {
    console.error('Error fetching transcription:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch transcription',
      message: error.message
    });
  }
});

// Create new transcription (manual)
router.post('/', async (req, res) => {
  try {
    const { video_id, title, content, language } = req.body;

    if (!video_id || !title || !content) {
      return res.status(400).json({
        success: false,
        error: 'video_id, title, and content are required'
      });
    }

    const transcription = await Transcription.create({
      video_id,
      title,
      content,
      language: language || 'en'
    });
    
    res.status(201).json({
      success: true,
      data: transcription.toJSON(),
      message: 'Transcription created successfully'
    });
  } catch (error) {
    console.error('Error creating transcription:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create transcription',
      message: error.message
    });
  }
});

// Update transcription
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const transcription = await Transcription.findById(id);
    if (!transcription) {
      return res.status(404).json({
        success: false,
        error: 'Transcription not found'
      });
    }

    const updatedTranscription = await transcription.update(updateData);
    
    res.json({
      success: true,
      data: updatedTranscription.toJSON(),
      message: 'Transcription updated successfully'
    });
  } catch (error) {
    console.error('Error updating transcription:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update transcription',
      message: error.message
    });
  }
});



// Get transcription statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const stats = await transcriptionService.getTranscriptionStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching transcription stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch transcription statistics',
      message: error.message
    });
  }
});

// Search transcriptions
router.get('/search/:query', async (req, res) => {
  try {
    const { query } = req.params;
    const { chapter_id } = req.query;

    if (!query || query.trim() === '') {
      return res.status(400).json({
        success: false,
        error: 'Search query is required'
      });
    }

    // Simple search implementation
    let transcriptions;
    if (chapter_id) {
      transcriptions = await Transcription.findByChapterId(chapter_id);
    } else {
      transcriptions = await Transcription.findAllWithDetails();
    }

    // Filter transcriptions that contain the search query
    const searchResults = transcriptions.filter(transcription => 
      transcription.title.toLowerCase().includes(query.toLowerCase()) ||
      transcription.content.toLowerCase().includes(query.toLowerCase()) ||
      (transcription.video_title && transcription.video_title.toLowerCase().includes(query.toLowerCase()))
    );

    res.json({
      success: true,
      data: searchResults,
      query: query,
      total: searchResults.length
    });
  } catch (error) {
    console.error('Error searching transcriptions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search transcriptions',
      message: error.message
    });
  }
});

// Export transcriptions for a chapter
router.get('/export/chapter/:chapter_id', async (req, res) => {
  try {
    const { chapter_id } = req.params;
    const { format } = req.query; // 'json' or 'text'

    const transcriptions = await Transcription.findByChapterId(chapter_id);

    if (transcriptions.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No transcriptions found for this chapter'
      });
    }

    if (format === 'text') {
      // Export as plain text
      let textContent = '';
      transcriptions.forEach((transcription, index) => {
        textContent += `=== ${transcription.title} ===\n\n`;
        textContent += `${transcription.content}\n\n`;
        if (index < transcriptions.length - 1) {
          textContent += '---\n\n';
        }
      });

      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename="chapter_${chapter_id}_transcriptions.txt"`);
      res.send(textContent);
    } else {
      // Export as JSON (default)
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="chapter_${chapter_id}_transcriptions.json"`);
      res.json({
        success: true,
        chapter_id: chapter_id,
        export_date: new Date().toISOString(),
        transcriptions: transcriptions
      });
    }
  } catch (error) {
    console.error('Error exporting transcriptions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export transcriptions',
      message: error.message
    });
  }
});

// Bulk delete transcriptions
router.post('/bulk-delete', async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Array of transcription IDs is required'
      });
    }

    let deletedCount = 0;
    let errorCount = 0;
    const errors = [];

    for (const id of ids) {
      try {
        const transcription = await Transcription.findById(id);
        if (transcription) {
          await transcription.delete();
          deletedCount++;
        } else {
          errorCount++;
          errors.push(`Transcription with ID ${id} not found`);
        }
      } catch (error) {
        errorCount++;
        errors.push(`Failed to delete transcription ${id}: ${error.message}`);
        console.error(`Error deleting transcription ${id}:`, error);
      }
    }

    res.json({
      success: true,
      message: `Bulk deletion completed: ${deletedCount} deleted, ${errorCount} errors`,
      data: {
        deleted: deletedCount,
        errors: errorCount,
        total: ids.length,
        errorDetails: errors
      }
    });
  } catch (error) {
    console.error('Error in bulk delete transcriptions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to bulk delete transcriptions',
      message: error.message
    });
  }
});

// Delete transcription by ID
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const transcription = await Transcription.findById(id);

    if (!transcription) {
      return res.status(404).json({
        success: false,
        error: 'Transcription not found'
      });
    }

    // Delete the transcription
    await transcription.delete();

    res.json({
      success: true,
      message: 'Transcription deleted successfully',
      data: { id: parseInt(id) }
    });
  } catch (error) {
    console.error('Error deleting transcription:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete transcription',
      message: error.message
    });
  }
});

module.exports = router;
